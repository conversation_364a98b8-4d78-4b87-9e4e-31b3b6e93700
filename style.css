body {
  margin: 0;
  background: #181820;
  color: #fff;
  font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: rgba(20,20,30,0.95);
  position: sticky;
  top: 0;
  z-index: 10;
}
.logo {
  height: 40px;
}
.menu-btn img {
  width: 32px;
  height: 32px;
}
.download-btn {
  background: #2ecc71;
  color: #fff;
  padding: 8px 18px;
  border-radius: 20px;
  text-decoration: none;
  font-weight: bold;
}
.banner {
  width: 100%;
  position: relative;
}
.banner-img {
  width: 100%;
  display: block;
  max-height: 100%;
  object-fit: cover;
}
.download-section {
  position: absolute;
  left: 50%;
  bottom: 2%;
  transform: translateX(-50%);
 
 
  padding: 18px 20px 12px 20px;
 
  text-align: center;
  z-index: 2;
}
 
.store-links {
  display: flex;
  justify-content: center;
  gap: 12px;
 
}
.store-links img {
  width: 120px;
}
.jobs-section {
  background: #080000;
  padding: 32px 16px 24px 16px;
  text-align: center;
}
.jobs-section h2 {
  font-size: 2rem;
  margin-bottom: 8px;
  letter-spacing: 2px;
}
.jobs-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2px;
  margin-top: 24px;
  justify-items: center;
  max-width: 370px;
  margin-left: auto;
  margin-right: auto;
}
.job-card {
  position: relative;
  width: 100%;
  max-width: 180px;
  aspect-ratio: 0.6;
  cursor: pointer;
  transition: transform 0.3s ease;
}
.job-card:hover {
  transform: scale(1.05);
}
.bonus-section {
  background: #080000;
  text-align: center;
  position: relative;
  padding: 20px 0;
}

/* 轮播图标题图片 */
.carousel-title-img {
  width: 80%;
  max-width: 300px;
  margin-bottom: 20px;
  object-fit: contain;
}

/* 轮播图容器 */
.carousel-container {
  position: relative;
  width: 90%;
  max-width: 420px;
  margin: 0 auto;
  border-radius: 16px;
  overflow: visible;
}

/* 轮播边框图片 - 完全覆盖轮播容器 */
.carousel-border {
  position: absolute;
  top: -15px;
  left: -15px;
  width: calc(100% + 30px);
  height: calc(100% + 30px);
  object-fit: fill;
  z-index: 15;
  pointer-events: none;
  user-select: none;
}

.carousel-wrapper {
  position: relative;
  width: 100%;
  height: 234px;
}

.carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}

.carousel-slide.active {
  opacity: 1;
}

.carousel-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 16px;
}

/* 轮播指示器 */
.carousel-indicators {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  z-index: 10;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: background 0.3s ease;
}

.indicator.active {
  background: #ffd700;
}

/* 轮播箭头 */
.carousel-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 20px;
  cursor: pointer;
  transition: background 0.3s ease;
  z-index: 10;
}

.carousel-btn:hover {
  background: rgba(0, 0, 0, 0.8);
}

.carousel-btn.prev {
  left: 10px;
}

.carousel-btn.next {
  right: 10px;
}
.footer {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  color: #c9c9c9;
  text-align: center;
  padding: 30px 20px 20px 20px;
  font-size: 0.9rem;
  position: relative;
  border-top: 2px solid #ffd700;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #ffd700 50%, transparent 100%);
}

.footer-links {
  margin-bottom: 15px;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 20px;
}

.footer-links a {
  color: #e0e0e0;
  text-decoration: none;
  padding: 8px 16px;
  border-radius: 20px;
  background: rgba(255, 215, 0, 0.1);
  border: 1px solid rgba(255, 215, 0, 0.3);
  transition: all 0.3s ease;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.footer-links a::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.2), transparent);
  transition: left 0.5s ease;
}

.footer-links a:hover {
  color: #ffd700;
  border-color: #ffd700;
  background: rgba(255, 215, 0, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.footer-links a:hover::before {
  left: 100%;
}

/* 页脚装饰 */
.footer-decoration {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  gap: 15px;
}

.footer-title {
  color: #ffd700;
  font-size: 1.2rem;
  font-weight: bold;
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
  letter-spacing: 2px;
}

.footer-ornament {
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #ffd700, transparent);
  position: relative;
}

.footer-ornament::before {
  content: '◆';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ffd700;
  font-size: 12px;
  background: #1a1a2e;
  padding: 2px;
}

/* 链接图标样式 */
.footer-link {
  display: flex;
  align-items: center;
  gap: 8px;
}

.link-icon {
  font-size: 14px;
  opacity: 0.8;
}

/* 分割线 */
.footer-divider {
  width: 80%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.3), transparent);
  margin: 20px auto 15px auto;
}

/* 版权信息样式 */
.footer-copyright {
  margin: 0;
  color: #b0b0b0;
  font-size: 0.85rem;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 6px;
}

.copyright-symbol {
  color: #ffd700;
  font-weight: bold;
}

.copyright-year {
  color: #ffd700;
  font-weight: 600;
}

.company-name {
  color: #e0e0e0;
  font-weight: 500;
}

.rights-text {
  color: #a0a0a0;
}

/* 页脚响应式设计 */
@media (max-width: 768px) {
  .footer {
    padding: 25px 15px 15px 15px;
  }

  .footer-decoration {
    margin-bottom: 15px;
    gap: 10px;
  }

  .footer-title {
    font-size: 1rem;
    letter-spacing: 1px;
  }

  .footer-ornament {
    width: 40px;
  }

  .footer-links {
    gap: 15px;
    flex-direction: column;
    align-items: center;
  }

  .footer-links a {
    padding: 6px 12px;
    font-size: 0.85rem;
  }

  .footer-copyright {
    font-size: 0.8rem;
    gap: 4px;
    text-align: center;
  }

  .footer-divider {
    width: 90%;
    margin: 15px auto 10px auto;
  }
}
.job-title-img {
  display: block;
  margin: 0 auto 18px auto;
  max-width: 90%;
  height: auto;
}

/* 职业图片容器 */
.job-img {
  position: absolute;
  top: 2%;
  left: 12%;
  width: 76%;
  height: 95%;
  object-fit: cover;
  z-index: 1;
  border-radius: 4px;
}

/* 边框图片 - 完全覆盖整个卡片 */
.job-border {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
  z-index: 2;
  pointer-events: none;
  user-select: none;
}

/* 十字标记 - 位于卡片底部中央 */
.job-cross {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 40px;
  object-fit: contain;
  z-index: 3;
  pointer-events: none;
  user-select: none;
}
@media (max-width: 600px) {
  .jobs-section {
    padding: 24px 12px 18px 12px;
  }
  .jobs-list {
    gap: 6px;
    max-width: 290px;
  }
  .job-card {
    max-width: 140px;
  }
  .job-img {
    top: 2%;
    left: 12%;
    width: 77%;
    height: 95%;
    border-radius: 3px;
  }
  .bonus-img {
    width: 98%;
  }
  .download-section {
    padding: 10px 4px 6px 4px;
  }
  .main-logo {
    height: 80px;
    margin-bottom: 12px;
  }
  .store-links img {
    width: 148px;
    margin: -12px  10px;
  }
}