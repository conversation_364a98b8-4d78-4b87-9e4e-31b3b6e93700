body {
  margin: 0;
  background: #181820;
  color: #fff;
  font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: rgba(20,20,30,0.95);
  position: sticky;
  top: 0;
  z-index: 10;
}
.logo {
  height: 40px;
}
.menu-btn img {
  width: 32px;
  height: 32px;
}
.download-btn {
  background: #2ecc71;
  color: #fff;
  padding: 8px 18px;
  border-radius: 20px;
  text-decoration: none;
  font-weight: bold;
}
.banner {
  width: 100%;
  position: relative;
}
.banner-img {
  width: 100%;
  display: block;
  max-height: 100%;
  object-fit: cover;
}
.download-section {
  position: absolute;
  left: 50%;
  bottom: 2%;
  transform: translateX(-50%);
 
 
  padding: 18px 20px 12px 20px;
 
  text-align: center;
  z-index: 2;
}
 
.store-links {
  display: flex;
  justify-content: center;
  gap: 12px;
 
}
.store-links img {
  width: 120px;
}
.jobs-section {
  background: #080000;
  padding: 32px 16px 24px 16px;
  text-align: center;
}
.jobs-section h2 {
  font-size: 2rem;
  margin-bottom: 8px;
  letter-spacing: 2px;
}
.jobs-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2px;
  margin-top: 24px;
  justify-items: center;
  max-width: 370px;
  margin-left: auto;
  margin-right: auto;
}
.job-card {
  position: relative;
  width: 100%;
  max-width: 180px;
  aspect-ratio: 0.6;
  cursor: pointer;
  transition: transform 0.3s ease;
}
.job-card:hover {
  transform: scale(1.05);
}
.bonus-section {
  background: #080000;
  text-align: center;
  position: relative;
  padding: 20px 0;
}

/* 轮播图标题图片 */
.carousel-title-img {
  width: 80%;
  max-width: 300px;
  margin-bottom: 20px;
  object-fit: contain;
}

/* 轮播图容器 */
.carousel-container {
  position: relative;
  width: 90%;
  max-width: 420px;
  margin: 0 auto;
  border-radius: 16px;
  overflow: visible;
}

/* 轮播边框图片 - 完全覆盖轮播容器 */
.carousel-border {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
  z-index: 15;
  pointer-events: none;
  user-select: none;
}

.carousel-wrapper {
  position: relative;
  width: 100%;
  height: 234px;
}

.carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}

.carousel-slide.active {
  opacity: 1;
}

.carousel-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 16px;
}

/* 轮播指示器 */
.carousel-indicators {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  z-index: 10;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: background 0.3s ease;
}

.indicator.active {
  background: #ffd700;
}

/* 轮播箭头 */
.carousel-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 20px;
  cursor: pointer;
  transition: background 0.3s ease;
  z-index: 10;
}

.carousel-btn:hover {
  background: rgba(0, 0, 0, 0.8);
}

.carousel-btn.prev {
  left: 10px;
}

.carousel-btn.next {
  right: 10px;
}
.footer {
  background: #181820;
  color: #aaa;
  text-align: center;
  padding: 18px 0 8px 0;
  font-size: 0.95rem;
}
.footer-links {
  margin-bottom: 8px;
}
.footer-links a {
  color: #aaa;
  margin: 0 10px;
  text-decoration: none;
}
.job-title-img {
  display: block;
  margin: 0 auto 18px auto;
  max-width: 90%;
  height: auto;
}

/* 职业图片容器 */
.job-img {
  position: absolute;
  top: 2%;
  left: 12%;
  width: 76%;
  height: 95%;
  object-fit: cover;
  z-index: 1;
  border-radius: 4px;
}

/* 边框图片 - 完全覆盖整个卡片 */
.job-border {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
  z-index: 2;
  pointer-events: none;
  user-select: none;
}

/* 十字标记 - 位于卡片底部中央 */
.job-cross {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 40px;
  object-fit: contain;
  z-index: 3;
  pointer-events: none;
  user-select: none;
}
@media (max-width: 600px) {
  .jobs-section {
    padding: 24px 12px 18px 12px;
  }
  .jobs-list {
    gap: 6px;
    max-width: 290px;
  }
  .job-card {
    max-width: 140px;
  }
  .job-img {
    top: 2%;
    left: 12%;
    width: 77%;
    height: 95%;
    border-radius: 3px;
  }
  .bonus-img {
    width: 98%;
  }
  .download-section {
    padding: 10px 4px 6px 4px;
  }
  .main-logo {
    height: 80px;
    margin-bottom: 12px;
  }
  .store-links img {
    width: 148px;
    margin: -12px  10px;
  }
}