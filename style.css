body {
  margin: 0;
  background: #181820;
  color: #fff;
  font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: rgba(20,20,30,0.95);
  position: sticky;
  top: 0;
  z-index: 10;
}
.logo {
  height: 40px;
}
.menu-btn img {
  width: 32px;
  height: 32px;
}
.download-btn {
  background: #2ecc71;
  color: #fff;
  padding: 8px 18px;
  border-radius: 20px;
  text-decoration: none;
  font-weight: bold;
}
.banner {
  width: 100%;
  position: relative;
}
.banner-img {
  width: 100%;
  display: block;
  max-height: 100%;
  object-fit: cover;
}
.download-section {
  position: absolute;
  left: 50%;
  bottom: 0%;
  transform: translateX(-50%);
 
 
  padding: 18px 20px 12px 20px;
 
  text-align: center;
  z-index: 2;
}
 
.store-links {
  display: flex;
  justify-content: center;
  gap: 8px;
 
}
.store-links img {
  width: 120px;
}
.jobs-section {
  background: #080000;
  padding: 32px 16px 24px 16px;
  text-align: center;
}
.jobs-section h2 {
  font-size: 2rem;
  margin-bottom: 8px;
  letter-spacing: 2px;
}
.jobs-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-top: 24px;
  justify-items: center;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}
.job-card {
  position: relative;
  width: 100%;
  max-width: 180px;
  aspect-ratio: 0.6;
  cursor: pointer;
  transition: transform 0.3s ease;
}
.job-card:hover {
  transform: scale(1.05);
}
.bonus-section {
  margin: 32px 0 0 0;
  text-align: center;
  position: relative;
}
.bonus-img {
  width: 90%;
  max-width: 420px;
  border-radius: 16px;
  box-shadow: 0 4px 16px #000a;
}
.bonus-text {
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translate(-50%, -30%);
  color: #fff;
  text-shadow: 0 2px 8px #000;
  width: 100%;
  pointer-events: none;
}
.footer {
  background: #181820;
  color: #aaa;
  text-align: center;
  padding: 18px 0 8px 0;
  font-size: 0.95rem;
}
.footer-links {
  margin-bottom: 8px;
}
.footer-links a {
  color: #aaa;
  margin: 0 10px;
  text-decoration: none;
}
.job-title-img {
  display: block;
  margin: 0 auto 18px auto;
  max-width: 90%;
  height: auto;
}

/* 职业图片容器 */
.job-img {
  position: absolute;
  top: 12%;
  left: 12%;
  width: 76%;
  height: 76%;
  object-fit: cover;
  z-index: 1;
  border-radius: 8px;
}

/* 边框图片 - 完全覆盖整个卡片 */
.job-border {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
  z-index: 2;
  pointer-events: none;
  user-select: none;
}
@media (max-width: 600px) {
  .jobs-list {
    gap: 8px;
  }
  .job-card {
    width: 44vw;
    max-width: 140px;
    min-width: 70px;
    aspect-ratio: 3/5;
  }
  .job-img {
    top: 4px;
    left: 4px;
    width: calc(100% - 8px);
    height: calc(100% - 8px);
  }
  .bonus-img {
    width: 98%;
  }
  .download-section {
    padding: 10px 4px 6px 4px;
  }
  .main-logo {
    height: 80px;
    margin-bottom: 12px;
  }
  .store-links img {
    width: 90px;
    margin: 0 4px;
  }
} 