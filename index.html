<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <title>新手游落地页</title>
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <!-- 顶部LOGO+菜单 -->
  <header class="header">
    <img src="./image/02.png" alt="游戏LOGO" class="logo">
    <!-- <div class="menu-btn">
      <img src="menu.svg" alt="菜单">
    </div>
    <a href="#" class="download-btn">下载游戏</a> -->
  </header>

  <!-- Banner主视觉 -->
  <section class="banner">
    <img src="./image/01.png" alt="主视觉" class="banner-img">
    <div class="download-section">
      <img src="./image/02.png" alt="游戏LOGO" class="main-logo">
      <div class="store-links">
        <a href="#"><img src="./image/03-1.png" alt="App Store"></a>
        <a href="#"><img src="./image/03-2.png" alt="Google Play"></a>
    
      </div>
    </div>
  </section>

  <!-- 职业介绍 -->
  <section class="jobs-section">
    <img src="./image/04.png" alt="职业介绍" class="job-title-img">
    <div class="jobs-list">
      <div class="job-card">
        <img src="./image/05.png" alt="边框" class="job-border">
        <img src="./image/09.png" alt="职业1" class="job-img">
        <img src="./image/06.png" alt="十字标记" class="job-cross">
      </div>
      <div class="job-card">
        <img src="./image/05.png" alt="边框" class="job-border">
        <img src="./image/10.png" alt="职业2" class="job-img">
        <img src="./image/06.png" alt="十字标记" class="job-cross">
      </div>
      <div class="job-card">
        <img src="./image/05.png" alt="边框" class="job-border">
        <img src="./image/11.png" alt="职业3" class="job-img">
        <img src="./image/06.png" alt="十字标记" class="job-cross">
      </div>
      <div class="job-card">
        <img src="./image/05.png" alt="边框" class="job-border">
        <img src="./image/12.png" alt="职业4" class="job-img">
        <img src="./image/06.png" alt="十字标记" class="job-cross">
      </div>
    </div>
  </section>

  <!-- 游戏介绍Banner -->
  <section class="bonus-section">
    <img src="./image/13.png" alt="游戏介绍标题" class="carousel-title-img">
    <div class="carousel-container">
      <div class="carousel-wrapper">
        <div class="carousel-slide active">
          <img src="./image/l1.png" alt="游戏介绍1" class="carousel-img">
        </div>
        <div class="carousel-slide">
          <img src="./image/l2.png" alt="游戏介绍2" class="carousel-img">
        </div>
        <div class="carousel-slide">
          <img src="./image/l3.png" alt="游戏介绍3" class="carousel-img">
        </div>
        <div class="carousel-slide">
          <img src="./image/l4.png" alt="游戏介绍4" class="carousel-img">
        </div>
        <div class="carousel-slide">
          <img src="./image/l5.png" alt="游戏介绍5" class="carousel-img">
        </div>
        <div class="carousel-slide">
          <img src="./image/l6.png" alt="游戏介绍6" class="carousel-img">
        </div>
      </div>

      <!-- 轮播指示器 -->
      <div class="carousel-indicators">
        <span class="indicator active" data-slide="0"></span>
        <span class="indicator" data-slide="1"></span>
        <span class="indicator" data-slide="2"></span>
        <span class="indicator" data-slide="3"></span>
        <span class="indicator" data-slide="4"></span>
        <span class="indicator" data-slide="5"></span>
      </div>

      <!-- 左右箭头 -->
      <button class="carousel-btn prev" onclick="changeSlide(-1)">‹</button>
      <button class="carousel-btn next" onclick="changeSlide(1)">›</button>
    </div>
  </section>

  <!-- 底部推荐/更多内容 -->
  <footer class="footer">
    <div class="footer-links">
      <a href="#">用户协议</a>
      <a href="#">隐私政策</a>
      <a href="#">联系我们</a>
    </div>
    <p>© 2024 新手游 All Rights Reserved.</p>
  </footer>

  <script>
    let currentSlide = 0;
    const slides = document.querySelectorAll('.carousel-slide');
    const indicators = document.querySelectorAll('.indicator');
    const totalSlides = slides.length;

    // 显示指定的幻灯片
    function showSlide(index) {
      // 移除所有active类
      slides.forEach(slide => slide.classList.remove('active'));
      indicators.forEach(indicator => indicator.classList.remove('active'));

      // 添加active类到当前幻灯片
      slides[index].classList.add('active');
      indicators[index].classList.add('active');

      currentSlide = index;
    }

    // 切换幻灯片
    function changeSlide(direction) {
      currentSlide += direction;

      if (currentSlide >= totalSlides) {
        currentSlide = 0;
      } else if (currentSlide < 0) {
        currentSlide = totalSlides - 1;
      }

      showSlide(currentSlide);
    }

    // 点击指示器切换
    indicators.forEach((indicator, index) => {
      indicator.addEventListener('click', () => {
        showSlide(index);
      });
    });

    // 自动轮播
    setInterval(() => {
      changeSlide(1);
    }, 4000); // 每4秒切换一次
  </script>
</body>
</html>